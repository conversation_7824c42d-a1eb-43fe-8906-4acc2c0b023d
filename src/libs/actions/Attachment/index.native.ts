import RNFetchBlob from 'react-native-blob-util';
import <PERSON>NF<PERSON> from 'react-native-fs';
import Onyx, {OnyxCollection} from 'react-native-onyx';
import {FileObject} from '@components/AttachmentModal';
import ONYXKEYS from '@src/ONYXKEYS';
import type {Attachment} from '@src/types/onyx';

let attachments: OnyxCollection<Attachment> | undefined;
Onyx.connect({
    key: ONYXKEYS.COLLECTION.ATTACHMENT,
    waitForCollectionCallback: true,
    callback: (value) => (attachments = value),
});

function storeAttachment(attachmentID: string, uri: string) {
    if (!attachmentID || !uri) {
        console.log('leeh2');
        return;
    }

    if (uri.startsWith('file://')) {
        console.log('leeh44');
        Onyx.set(`${ONYXKEYS.COLLECTION.ATTACHMENT}${attachmentID}`, {
            attachmentID,
            source: uri,
        });
        return;
    }

    const attachment = attachments?.[attachmentID];

    if (attachment?.source && attachment.remoteSource === uri) {
        console.log('leeh');
        return;
    }

    RNFetchBlob.config({fileCache: true})
        .fetch('GET', uri)
        .then((response) => {
            const filePath = response.path();
            console.log('filePath', filePath);
            Onyx.set(`${ONYXKEYS.COLLECTION.ATTACHMENT}${attachmentID}`, {
                attachmentID,
                source: filePath,
            }).then((d) => {
                console.log('d', d);
            });
        })
        .catch((error) => {
            console.log('error', error);
        });
}

function getAttachmentSource(attachmentID: string) {
    if (!attachmentID) {
        return;
    }
    const attachment = attachments?.[`${ONYXKEYS.COLLECTION.ATTACHMENT}${attachmentID}`];
    console.log('attachments', `${ONYXKEYS.COLLECTION.ATTACHMENT}${attachmentID}`, attachment, attachments);
    return attachment?.source;
}

export {storeAttachment, getAttachmentSource};
